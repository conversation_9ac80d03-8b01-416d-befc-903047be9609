"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import {
  Users,
  Calendar,
  MessageSquare,
  Heart,
  ArrowRight,
  Clock,
  ExternalLink
} from "lucide-react"

export function RecentActivity() {
  const { data: dashboardData, isLoading } = api.analytics.getUserDashboardStats.useQuery()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  // Convert real activity data to display format
  const getActivityDisplay = (type: string) => {
      switch (type) {
        case 'DONATION_CREATED':
          return {
            action: 'made a donation',
            icon: Heart,
            color: 'text-red-600'
          }
        case 'DONATION_COMPLETED':
          return {
            action: 'completed a donation',
            icon: Heart,
            color: 'text-green-600'
          }
        case 'POST_CREATED':
          return {
            action: 'created a post',
            icon: MessageSquare,
            color: 'text-blue-600'
          }
        case 'EVENT_RSVP':
          return {
            action: 'RSVP\'d to an event',
            icon: Calendar,
            color: 'text-purple-600'
          }
        case 'PROFILE_UPDATED':
          return {
            action: 'updated their profile',
            icon: Users,
            color: 'text-orange-600'
          }
        default:
          return {
            action: 'performed an action',
            icon: Clock,
            color: 'text-gray-600'
          }
      }
    }

  const activities = (dashboardData?.recentActivity || []).map(activity => {
    const display = getActivityDisplay(activity.type)
    const timeAgo = new Date(activity.time).toLocaleDateString()

    return {
      id: activity.id,
      type: activity.type.toLowerCase(),
      user: {
        name: 'You', // Since these are user's own activities
        avatar: null,
        initials: 'ME',
        role: 'Your Activity'
      },
      action: display.action,
      time: timeAgo,
      icon: display.icon,
      color: display.color
    }
  })

  // Use real activities or show empty state
  const displayActivities = activities.length > 0 ? activities : []

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Recent Activity
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/activity">
            View All
            <ExternalLink className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {displayActivities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-8 w-8 mx-auto mb-2" />
            <p>No recent activity</p>
          </div>
        ) : (
          displayActivities.map((activity) => {
          const Icon = activity.icon
          return (
            <div key={activity.id} className="flex items-start space-x-3 group">
              <div className="relative">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                  <AvatarFallback className="bg-protec-navy text-white text-sm">
                    {activity.user.initials}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-white flex items-center justify-center border-2 border-white`}>
                  <Icon className={`h-3 w-3 ${activity.color}`} />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-protec-navy truncate">
                    {activity.user.name}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {activity.user.role.split(' ')[0]}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {activity.action}
                </p>
                <div className="flex items-center space-x-1 mt-1">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {activity.time}
                  </span>
                </div>
              </div>
            </div>
          )
        })
        )}
      </CardContent>
    </Card>
  )
}
