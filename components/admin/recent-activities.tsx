"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import { 
  Activity, 
  UserPlus, 
  MessageCircle, 
  Calendar,
  DollarSign,
  Users,
  FileText,
  Award
} from "lucide-react"

interface RecentActivitiesProps {
  className?: string
}

export function RecentActivities({ className }: RecentActivitiesProps) {
  const { data: activitiesData, isLoading } = api.analytics.getRecentActivities.useQuery({
    limit: 10
  })

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const recentActivities = activitiesData?.activities || []
    {
      id: '1',
      type: 'donation',
      user: {
        name: 'John Smith',
        photoUrl: null
      },
      description: 'Made a donation of R500',
      timestamp: '2024-03-15T10:30:00Z',
      metadata: { amount: 500, purpose: 'General Fund' }
    },
    {
      id: '2',
      type: 'connection',
      user: {
        name: 'Sarah Johnson',
        photoUrl: null
      },
      description: 'Connected with Michael Brown',
      timestamp: '2024-03-15T09:45:00Z',
      metadata: { connectedWith: 'Michael Brown' }
    },
    {
      id: '3',
      type: 'event_rsvp',
      user: {
        name: 'Emily Davis',
        photoUrl: null
      },
      description: 'RSVP\'d to Annual Alumni Gala',
      timestamp: '2024-03-15T09:15:00Z',
      metadata: { eventName: 'Annual Alumni Gala' }
    },
    {
      id: '4',
      type: 'profile_update',
      user: {
        name: 'David Wilson',
        photoUrl: null
      },
      description: 'Updated profile information',
      timestamp: '2024-03-15T08:30:00Z',
      metadata: { fields: ['currentRole', 'company'] }
    },
    {
      id: '5',
      type: 'message',
      user: {
        name: 'Lisa Anderson',
        photoUrl: null
      },
      description: 'Sent a message to Robert Taylor',
      timestamp: '2024-03-15T08:00:00Z',
      metadata: { recipient: 'Robert Taylor' }
    },
    {
      id: '6',
      type: 'post',
      user: {
        name: 'Mark Johnson',
        photoUrl: null
      },
      description: 'Created a new post about career opportunities',
      timestamp: '2024-03-15T07:45:00Z',
      metadata: { postTitle: 'New Opportunities in Tech' }
    },
    {
      id: '7',
      type: 'volunteer',
      user: {
        name: 'Jennifer White',
        photoUrl: null
      },
      description: 'Volunteered for mentorship program',
      timestamp: '2024-03-15T07:00:00Z',
      metadata: { program: 'Career Mentorship' }
    },
    {
      id: '8',
      type: 'registration',
      user: {
        name: 'Robert Taylor',
        photoUrl: null
      },
      description: 'Completed profile registration',
      timestamp: '2024-03-15T06:30:00Z',
      metadata: { graduationYear: 2020 }
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'donation':
        return <DollarSign className="h-4 w-4 text-green-600" />
      case 'connection':
        return <UserPlus className="h-4 w-4 text-blue-600" />
      case 'event_rsvp':
        return <Calendar className="h-4 w-4 text-purple-600" />
      case 'profile_update':
        return <Users className="h-4 w-4 text-orange-600" />
      case 'message':
        return <MessageCircle className="h-4 w-4 text-indigo-600" />
      case 'post':
        return <FileText className="h-4 w-4 text-gray-600" />
      case 'volunteer':
        return <Award className="h-4 w-4 text-red-600" />
      case 'registration':
        return <UserPlus className="h-4 w-4 text-green-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'donation':
        return 'bg-green-100 text-green-800'
      case 'connection':
        return 'bg-blue-100 text-blue-800'
      case 'event_rsvp':
        return 'bg-purple-100 text-purple-800'
      case 'profile_update':
        return 'bg-orange-100 text-orange-800'
      case 'message':
        return 'bg-indigo-100 text-indigo-800'
      case 'post':
        return 'bg-gray-100 text-gray-800'
      case 'volunteer':
        return 'bg-red-100 text-red-800'
      case 'registration':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Recent Activities</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                    {getActivityIcon(activity.type)}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={activity.user.photoUrl || undefined} />
                      <AvatarFallback className="bg-protec-navy text-white text-xs">
                        {getInitials(activity.user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium text-sm">{activity.user.name}</span>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getActivityColor(activity.type)}`}
                    >
                      {activity.type.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-1">
                    {activity.description}
                  </p>
                  
                  {activity.metadata && (
                    <div className="text-xs text-muted-foreground">
                      {activity.type === 'donation' && (
                        <span>Amount: R{activity.metadata.amount} • Purpose: {activity.metadata.purpose}</span>
                      )}
                      {activity.type === 'connection' && (
                        <span>Connected with: {activity.metadata.connectedWith}</span>
                      )}
                      {activity.type === 'event_rsvp' && (
                        <span>Event: {activity.metadata.eventName}</span>
                      )}
                      {activity.type === 'profile_update' && (
                        <span>Updated: {activity.metadata.fields?.join(', ')}</span>
                      )}
                      {activity.type === 'message' && (
                        <span>To: {activity.metadata.recipient}</span>
                      )}
                      {activity.type === 'post' && (
                        <span>Title: {activity.metadata.postTitle}</span>
                      )}
                      {activity.type === 'volunteer' && (
                        <span>Program: {activity.metadata.program}</span>
                      )}
                      {activity.type === 'registration' && (
                        <span>Class of {activity.metadata.graduationYear}</span>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="flex-shrink-0 text-xs text-muted-foreground">
                  {formatTimeAgo(activity.timestamp)}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 text-center">
            <button className="text-sm text-protec-red hover:text-protec-red/80 font-medium">
              View All Activities
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
