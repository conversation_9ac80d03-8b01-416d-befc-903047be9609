import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Input validation schemas
const createPostSchema = z.object({
  content: z.string().min(1).max(2000),
  mediaUrls: z.array(z.string().url()).max(5).default([]),
  tags: z.array(z.string()).max(10).default([]),
})

const updatePostSchema = z.object({
  id: z.string().uuid(),
  content: z.string().min(1).max(2000).optional(),
  mediaUrls: z.array(z.string().url()).max(5).optional(),
  tags: z.array(z.string()).max(10).optional(),
})

const getPostsSchema = z.object({
  authorId: z.string().uuid().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().int().min(1).max(50).default(20),
  cursor: z.string().optional(),
})

const createCommentSchema = z.object({
  postId: z.string().uuid(),
  text: z.string().min(1).max(500),
})

const likePostSchema = z.object({
  postId: z.string().uuid(),
})

export const postsRouter = createTRPCRouter({
  // Get all posts with filtering
  getAll: publicProcedure
    .input(getPostsSchema)
    .query(async ({ ctx, input }) => {
      const { authorId, tags, limit, cursor } = input

      const where: any = {}

      if (authorId) {
        where.authorId = authorId
      }

      if (tags && tags.length > 0) {
        where.tags = { hasSome: tags }
      }

      const posts = await ctx.prisma.post.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          comments: {
            take: 3,
            orderBy: { createdAt: 'desc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                },
              },
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (posts.length > limit) {
        const nextItem = posts.pop()
        nextCursor = nextItem!.id
      }

      return {
        posts,
        nextCursor,
      }
    }),

  // Get post by ID
  getById: publicProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const post = await ctx.prisma.post.findUnique({
        where: { id: input.id },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          comments: {
            orderBy: { createdAt: 'asc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                },
              },
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      })

      if (!post) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Post not found',
        })
      }

      return post
    }),

  // Create new post
  create: protectedProcedure
    .input(createPostSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const post = await ctx.prisma.post.create({
        data: {
          ...input,
          authorId: currentAlumni.id,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'POST_CREATED',
          refId: post.id,
          alumniId: currentAlumni.id,
        },
      })

      return post
    }),

  // Update post
  update: protectedProcedure
    .input(updatePostSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if user is the author
      const post = await ctx.prisma.post.findUnique({
        where: { id },
      })

      if (!post) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Post not found',
        })
      }

      if (post.authorId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only the author can update this post',
        })
      }

      const updatedPost = await ctx.prisma.post.update({
        where: { id },
        data: updateData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      })

      return updatedPost
    }),

  // Delete post
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if user is the author
      const post = await ctx.prisma.post.findUnique({
        where: { id: input.id },
      })

      if (!post) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Post not found',
        })
      }

      if (post.authorId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only the author can delete this post',
        })
      }

      await ctx.prisma.post.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Like/unlike post
  toggleLike: protectedProcedure
    .input(likePostSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const post = await ctx.prisma.post.findUnique({
        where: { id: input.postId },
      })

      if (!post) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Post not found',
        })
      }

      const likes = post.likes as string[]
      const hasLiked = likes.includes(currentAlumni.id)

      const updatedLikes = hasLiked
        ? likes.filter(id => id !== currentAlumni.id)
        : [...likes, currentAlumni.id]

      const updatedPost = await ctx.prisma.post.update({
        where: { id: input.postId },
        data: { likes: updatedLikes },
      })

      // Log activity if liking (not unliking)
      if (!hasLiked) {
        await ctx.prisma.activity.create({
          data: {
            type: 'POST_LIKED',
            refId: input.postId,
            alumniId: currentAlumni.id,
          },
        })
      }

      return { liked: !hasLiked, likesCount: updatedLikes.length }
    }),

  // Add comment to post
  addComment: protectedProcedure
    .input(createCommentSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if post exists
      const post = await ctx.prisma.post.findUnique({
        where: { id: input.postId },
      })

      if (!post) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Post not found',
        })
      }

      const comment = await ctx.prisma.comment.create({
        data: {
          text: input.text,
          authorId: currentAlumni.id,
          postId: input.postId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
            },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'COMMENT_CREATED',
          refId: comment.id,
          alumniId: currentAlumni.id,
        },
      })

      return comment
    }),

  // Get comments for a post
  getComments: publicProcedure
    .input(z.object({ 
      postId: z.string().uuid(),
      limit: z.number().int().min(1).max(100).default(20),
      cursor: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { postId, limit, cursor } = input

      const comments = await ctx.prisma.comment.findMany({
        where: { postId },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'asc' },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (comments.length > limit) {
        const nextItem = comments.pop()
        nextCursor = nextItem!.id
      }

      return {
        comments,
        nextCursor,
      }
    }),

  // Get trending tags
  getTrendingTags: publicProcedure.query(async ({ ctx }) => {
    const posts = await ctx.prisma.post.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      select: { tags: true },
    })

    const tagCounts: Record<string, number> = {}
    posts.forEach(post => {
      post.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    })

    const trendingTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count }))

    return trendingTags
  }),

  // Get recent posts for dashboard
  getRecent: publicProcedure
    .input(z.object({
      limit: z.number().int().min(1).max(10).default(5)
    }))
    .query(async ({ ctx, input }) => {
      const posts = await ctx.prisma.post.findMany({
        take: input.limit,
        orderBy: { createdAt: 'desc' },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          _count: {
            select: {
              comments: true,
            },
          },
        },
      })

      return {
        posts: posts.map(post => ({
          ...post,
          likesCount: (post.likes as string[]).length,
          commentsCount: post._count.comments,
        }))
      }
    }),
})
